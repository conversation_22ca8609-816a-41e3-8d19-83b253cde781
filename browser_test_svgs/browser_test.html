<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid SVG Cross-Browser Compatibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .svg-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .browser-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .checklist {
            background: #f1f8e9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .checklist ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }
        .pass { background-color: #4caf50; }
        .fail { background-color: #f44336; }
        .unknown { background-color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mermaid SVG Cross-Browser Compatibility Test</h1>
        
        <div class="browser-info">
            <h3>Browser Information</h3>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Browser:</strong> <span id="browserName"></span></p>
            <p><strong>Version:</strong> <span id="browserVersion"></span></p>
            <p><strong>Platform:</strong> <span id="platform"></span></p>
        </div>
        
        <div class="test-case">
            <h3>Basic Flowchart Test</h3>
            <p>Tests basic shapes, text rendering, and arrow markers</p>
            <div class="svg-container">
                <object data="basic_flowchart.svg" type="image/svg+xml" width="300" height="200">
                    Your browser does not support SVG
                </object>
            </div>
        </div>
        
        <div class="test-case">
            <h3>Complex Shapes Test</h3>
            <p>Tests circles, ellipses, polygons, and curved paths</p>
            <div class="svg-container">
                <object data="complex_shapes.svg" type="image/svg+xml" width="400" height="300">
                    Your browser does not support SVG
                </object>
            </div>
        </div>
        
        <div class="test-case">
            <h3>Gradients and Patterns Test</h3>
            <p>Tests linear/radial gradients and pattern fills</p>
            <div class="svg-container">
                <object data="gradients_and_patterns.svg" type="image/svg+xml" width="350" height="250">
                    Your browser does not support SVG
                </object>
            </div>
        </div>
        
        <div class="test-case">
            <h3>Text and Fonts Test</h3>
            <p>Tests various font families, sizes, and styles</p>
            <div class="svg-container">
                <object data="text_and_fonts.svg" type="image/svg+xml" width="400" height="300">
                    Your browser does not support SVG
                </object>
            </div>
        </div>
        
        <div class="test-case">
            <h3>Animations Test</h3>
            <p>Tests SVG animations and transforms</p>
            <div class="svg-container">
                <object data="animations.svg" type="image/svg+xml" width="300" height="200">
                    Your browser does not support SVG
                </object>
            </div>
        </div>
        
        <div class="checklist">
            <h3>Testing Checklist</h3>
            <p>Please verify the following in your browser:</p>
            <ul>
                <li>All SVG images load and display correctly</li>
                <li>Text is readable and properly positioned</li>
                <li>Colors and gradients render correctly</li>
                <li>Shapes have proper borders and fills</li>
                <li>Animations play smoothly (if supported)</li>
                <li>SVGs scale properly when zooming</li>
                <li>No JavaScript errors in console</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Detect browser information
        const userAgent = navigator.userAgent;
        const platform = navigator.platform;
        
        document.getElementById('userAgent').textContent = userAgent;
        document.getElementById('platform').textContent = platform;
        
        // Simple browser detection
        let browserName = 'Unknown';
        let browserVersion = 'Unknown';
        
        if (userAgent.indexOf('Chrome') > -1) {
            browserName = 'Chrome';
            browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || 'Unknown';
        } else if (userAgent.indexOf('Firefox') > -1) {
            browserName = 'Firefox';
            browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)?.[1] || 'Unknown';
        } else if (userAgent.indexOf('Safari') > -1) {
            browserName = 'Safari';
            browserVersion = userAgent.match(/Version\/([0-9.]+)/)?.[1] || 'Unknown';
        } else if (userAgent.indexOf('Edge') > -1) {
            browserName = 'Edge';
            browserVersion = userAgent.match(/Edge\/([0-9.]+)/)?.[1] || 'Unknown';
        }
        
        document.getElementById('browserName').textContent = browserName;
        document.getElementById('browserVersion').textContent = browserVersion;
    </script>
</body>
</html>